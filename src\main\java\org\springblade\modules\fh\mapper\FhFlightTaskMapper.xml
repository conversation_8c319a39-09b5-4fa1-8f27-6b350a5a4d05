<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.fh.mapper.FhFlightTaskMapper">

    <!-- 根据UUID查询飞行任务 -->
    <select id="selectByUuid" resultType="org.springblade.modules.fh.pojo.entity.FhFlightTask">
        SELECT * FROM fh_flight_task
        WHERE uuid = #{uuid}
        AND is_deleted = 0
    </select>

    <!-- 获取总飞行时长（小时） -->
    <select id="getTotalFlightTime" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM( EXTRACT(EPOCH FROM (completed_at - run_at)) / 3600.0), 0) as total_flight_time
        FROM fh_flight_task
        WHERE task_status = 'success'
        AND run_at IS NOT NULL
        AND completed_at IS NOT NULL
        AND is_deleted = 0
    </select>

    <!-- 获取所有任务的年份列表（按年份降序排序） -->
    <select id="selectTaskYears" resultType="java.lang.Integer">
        SELECT DISTINCT EXTRACT(YEAR FROM begin_at)::integer as year
        FROM fh_flight_task
        WHERE begin_at IS NOT NULL
        AND is_deleted = 0
        ORDER BY year DESC
    </select>

</mapper>
